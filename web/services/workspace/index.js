import mysql from '../../services/database.js';
import logger from '../../services/logger.js';
import { cache } from '../../services/redis.js';
import UserInvitation, { USER_INVITATION_STATUS_PENDING } from '../user/invitation/index.js';
import { clearShopsByWorkspaceIDCache } from '../shop.js';
import user from '../user.js';
import WorkspaceUser from './workspace_user.js';
import integrations from '../../services/airbyte/integrations.js';
import { getActiveSubscription } from '../subscription.js';

class Workspace {

    constructor() {
        this.workspace_id = null;
    }

    setWorkspaceID(workspace_id) {
        this.workspace_id = workspace_id;
        return this;
    }

    static async clearCacheAllUsers(workspace_id) {
        let workspace_users = await WorkspaceUser.getAllByWorkspaceID(workspace_id);

        await WorkspaceUser.clearAllByWorkspaceIDCache(workspace_id);
        await clearShopsByWorkspaceIDCache(workspace_id);

        for (let wu of workspace_users) {
            await WorkspaceUser.clearAllByWorkspaceIDAndUserIDCache(workspace_id, wu.user_id);
            await WorkspaceUser.clearAllByWorkspaceIDAndEmailCache(workspace_id, wu.user_email);
            await WorkspaceUser.clearAllByEmailCache(wu.user_email);
            await Workspace.clearWorkspaceDetailsForUserCache(workspace_id, wu.user_id);
            await WorkspaceUser.clearUserWorkspacesCache(wu.user_id);
            await user.clearUserCache(wu.user_id, workspace_id);
        }
    }

    static #getWorkspaceDetailsForUserCacheKey(workspace_id, user_id) {
        return `workspace_details_for_user_v1_${workspace_id}_${user_id}`;
    }

    static async clearWorkspaceDetailsForUserCache(workspace_id, user_id) {
        var cacheKey = Workspace.#getWorkspaceDetailsForUserCacheKey(workspace_id, user_id);
        await cache.del(cacheKey);
    }

    async #getWorkspaceDetailsForUserFromDB(workspace_id, user_id) {

        var cacheKey = Workspace.#getWorkspaceDetailsForUserCacheKey(workspace_id, user_id);
        var fromCache = await cache.get(cacheKey);
        if (fromCache !== null) {
            return fromCache;
        }

        let result = {}
        try {
            const query = `
                SELECT
                    wu.workspace_id,
                    wu.role,
                    wu.shop_ids,
                    wu.status,
                    w.name as workspace_name,
                    ws.shop_id,
                    s.name as shop_name,
                    s.myshopify_domain
                FROM workspace_users wu
                JOIN workspaces w ON wu.workspace_id = w.workspace_id
                LEFT JOIN workspace_shops ws ON wu.workspace_id = ws.workspace_id AND ws.status = 1
                LEFT JOIN shops s ON ws.shop_id = s.shop_id AND s.active_flag = 1
                WHERE wu.user_id = ? AND wu.workspace_id = ? AND wu.status = 1
                AND (
                    wu.role in ('Owner', 'Admin') OR
                    FIND_IN_SET(ws.shop_id, wu.shop_ids) OR wu.shop_ids IS NULL
                )
                ORDER BY wu.workspace_id
            `
            const query_result = await mysql.query(query, [user_id, workspace_id]);

            if (query_result && query_result.length > 0) {
                const shops = query_result.map(r => ({
                    shop_id: r.shop_id,
                    shop_name: r.shop_name,
                    myshopify_domain: r.myshopify_domain
                }));

                result = {
                    workspace_id: query_result[0].workspace_id,
                    role: query_result[0].role,
                    shop_ids: query_result[0].shop_ids ? query_result[0].shop_ids.split(",") : [],
                    status: query_result[0].status,
                    workspace_name: query_result[0].workspace_name,
                    shops: shops
                }
            }
        } catch (err) {
            logger.error('Workspace->getWorkspaceDetailsForUserFromDB : failed to get workspace details', err);
            return {};
        }

        if (result) {
            await cache.set(cacheKey, result);
        }

        return result;
    }

    async getWorkspaceDetailsForUser(workspace_id, user_id) {
        let workspace_details = await this.#getWorkspaceDetailsForUserFromDB(workspace_id, user_id);

        if (!workspace_details || !workspace_details.workspace_id) {
            return {status: false, message: 'No workspace details found'};
        }

        if (workspace_details.shops && workspace_details.shops.length > 0) {
            const [workspaceUsersResult, pendingInvitationsResult] = await Promise.allSettled([
                WorkspaceUser.getAllByWorkspaceID(workspace_id),
                UserInvitation.getAllByWorkspaceIDAndStatus(workspace_id, USER_INVITATION_STATUS_PENDING)
            ]);

            workspace_details.users = workspaceUsersResult.value;
            workspace_details.pending_invitations = pendingInvitationsResult.value;

            // TODO: get integrations and subscriptions for multiple shops at once instead of one call for each shop
            await Promise.all(
                workspace_details.shops.flatMap(shop => [
                    integrations.getIntegrations(shop.shop_id, "", true, false)
                        .then(result => { shop.integrations = result; }),
                    getActiveSubscription(shop.shop_id)
                        .then(result => { shop.subscription = result; })
                ])
            );
        } else {
            return {status: false, message: 'No shops found in workspace'};
        }

        return {status: true, message: 'Workspace details fetched successfully', data: workspace_details};
    }

    async updateWorkspaceName(workspace_id, name) {
        if (!workspace_id || !name) {
            logger.error('Workspace->updateWorkspaceName : missing required params', {
                workspace_id: workspace_id,
                name: name
            });
            return {status: false, message: 'Missing required params'};
        }

        try {
            await mysql.query("UPDATE workspaces SET name = ? WHERE workspace_id = ?", [name, workspace_id]);

            // Clear all related caches
            await Workspace.clearCacheAllUsers(workspace_id);

            return {status: true, message: 'Workspace name updated successfully'};
        } catch (err) {
            logger.error('Workspace->updateWorkspaceName : failed to update workspace name', err);
            return {status: false, message: 'Failed to update workspace name'};
        }
    }
}

export default Workspace;
