import mysql from './database.js';
import logger from './logger.js';
import { cache } from './redis.js';
import { verificationEmailRender } from '../dist/index.js';
import shop from './shop.js';
import { getActiveSubscription } from './subscription.js';
import { SESv2Client, SendEmailCommand } from "@aws-sdk/client-sesv2";
import WorkspaceUser, { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from './workspace/workspace_user.js';
import Workspace from './workspace/index.js';

const sesClient = new SESv2Client({
    region: process.env.AWS_SES_REGION,
    credentials: {
        accessKeyId: process.env.AWS_SES_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SES_SECRET,
    },
});

async function getUserData(uid, selectedWorkspaceId = null) {
    var cacheKey = getUserDataCacheKey(uid, selectedWorkspaceId)
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    var user_data = {}
    try {
        // TODO: what should happen if user is part of multiple workspaces?
        let query = `
            SELECT 
                u.uid,
                COALESCE(wu.workspace_id, u.workspace_id) AS workspace_id,
                u.email,
                wu.role,
                u.onboard_email,
                u.onboard_name,
                u.onboard_phone,
                u.onboard_industry,
                u.onboard_user_type,
                w.name as workspace_name,
                w.workspace_type,
                u.row_created_at
            FROM users u
            LEFT JOIN workspace_users wu
                ON wu.user_id = u.uid
                AND wu.status = 1
            LEFT JOIN workspaces w ON w.workspace_id = COALESCE(wu.workspace_id, u.workspace_id)
            WHERE u.uid = ?
        `

        let params = [uid]

        if (selectedWorkspaceId && Number(selectedWorkspaceId) > 0) {
            query = query + `
                AND wu.workspace_id = ?
            `
            params.push(Number(selectedWorkspaceId))
        }

        query = query + `
            ORDER BY
                CASE WHEN wu.role = 'Owner' THEN 0 ELSE 1 END,
                CASE WHEN wu.role = 'Admin' THEN 1 ELSE 2 END
        `
        let result = await mysql.query(query, params);
        user_data = result[0]
    } catch (err) {
        logger.error('user.getUserData error : ', err)
    }

    if (user_data && user_data.uid) {
        await cache.set(cacheKey, user_data)
    }
    return user_data
}

async function getOrCreateOwnerWorkspaceID(uid, email, shop_ids = []) {
    let userWorkspaces = await WorkspaceUser.getUserWorkspaces(uid);
    if (userWorkspaces.some((w) => w.role == WORKSPACE_ROLE_OWNER)) {
        return userWorkspaces.find((w) => w.role == WORKSPACE_ROLE_OWNER).workspace_id;
    } else {
        // Extract the part before '@' from email for workspace name
        const workspaceName = email.split('@')[0];
        let workspace = await createWorkspace(workspaceName, shop.WORKSPACE_TYPE_OWNER)
        if (!workspace || !workspace.workspace_id) {
            logger.error('getOrCreateOwnerWorkspaceID : createWorkspace failed', {
                uid : uid,
                email : email
            })
            return null
        }
        await WorkspaceUser.addUserToWorkspace(workspace.workspace_id, uid, WORKSPACE_ROLE_OWNER, shop_ids.join(','))
        await cache.del(getUserDataCacheKey(uid))
        return workspace.workspace_id
    }
}

// after auth middleware, resolves user data
async function resolveUser(req, res, next) {
    let {user} = res.locals

    let userData = {};
    let user_selected_shop = '';
    let isAdmin = false;
    let userShops = [];
    let selectedWorkspaceId = req.body.selectedWorkspaceId || req.query.selectedWorkspaceId || null;


    if (!!user && !!user.uid) {
        userData = await getUserData(user.uid, selectedWorkspaceId)
        if (!userData  || !userData.email) {
            // save the user data when he authenticates for the first time
            await save(user)
            if (!(userData?.workspace_id ?? null)) {
                selectedWorkspaceId = await getOrCreateOwnerWorkspaceID(user.uid, user.email);
            }
            userData = await getUserData(user.uid, selectedWorkspaceId)
        }

        if (!userData  || !userData.email) {
            return res.status(401).send('Unauthorized');
        }

        let selectedShop = req.body.selectedShop || req.query.selectedShop;
        let mappedShops = await getUserShops(user.uid, selectedWorkspaceId)
        isAdmin = mappedShops.some((s) => s.myshopify_domain == process.env.TEST_STORE);
        for (var k in mappedShops) {
            let shopObj = mappedShops[k]
            if (!shopObj || !shopObj.myshopify_domain || !shopObj.active_flag) {
                continue
            }

            userShops.push(shopObj)
            if (shopObj.myshopify_domain === selectedShop) {
                // non-admin first check if selectedShop is valid
                user_selected_shop = selectedShop
            } else if (selectedShop == '' && shopObj.myshopify_domain == process.env.TEST_STORE) {
                // adminif selectedShop is not set, then use TEST_STORE
                user_selected_shop = process.env.TEST_STORE
            } else if (!selectedShop) {
                // non admin - but no shop selected - choose any of mappedShops
                user_selected_shop = shopObj.myshopify_domain
            }
        }
    }

    // user_data is authenticated & authorized user while res.locals.user is authenticated user
    req.body.user_data = userData; // user onboarded data in db
    req.body.user_shops = userShops; // shops user has access to
    req.body.is_admin = isAdmin; // admin user
    req.body.authed_user = user; // authenticated user
    req.body.resolved_login_id = !!user && !!user.email ? user.email : '';


    // next middleware can use this data securely 
    // passing it in serverAddedData instead of passing it in req.body
    req.serverAddedData = {
        is_admin : isAdmin,
        user_selected_shop : user_selected_shop,
        resolved_login_id : !!userData && !!userData.email ? userData.email : '',
    }

    return await next();
}

async function clearUserCache(uid, workspace_id = null) {
    async function clearUserCacheForWorkspace(uid, workspace_id) {
        let c1 = await cache.del(getUserDataCacheKey(uid, workspace_id));
        let c2 = await cache.del(getUserShopsCacheKey(uid, workspace_id));
        let c3 = await cache.del(getUserByIDCacheKey(uid));
        return c1 && c2 && c3;
    }

    if (!workspace_id) {
        // TODO: use workspace_ids from middleware once middleware is updated
        let user_workspaces = await WorkspaceUser.getUserWorkspaces(uid);
        for (let w of user_workspaces) {
            await clearUserCacheForWorkspace(uid, w.workspace_id);
        }
    } else {
        return await clearUserCacheForWorkspace(uid, workspace_id);
    }
}

function getUserByIDCacheKey(uid) {
    return `user_v4_${uid}`
}

function getUserByEmailCacheKey(email) {
    return `user_email_v1_${email}`
}

function getUserDataCacheKey(uid, selectedWorkspaceId = null) {
    return `user_data_v1_${uid}_${selectedWorkspaceId}`
}

function getUserShopsCacheKey(uid, selectedWorkspaceId = null) {
    return `user_shops_v1_${uid}_${selectedWorkspaceId}`
}

async function getUserShops(uid, selectedWorkspaceId = null) {
    var cacheKey = getUserShopsCacheKey(uid, selectedWorkspaceId)
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    var user_shops = []
    try {
        let query = `
            SELECT
                ws.shop_id,
                s.myshopify_domain,
                s.currency,
                s.active_flag,
                s.name,
                s.domain
            FROM users u
            LEFT JOIN workspace_users wu
                ON wu.user_id = u.uid
                AND wu.status = 1
            LEFT JOIN workspace_shops ws
                ON ws.workspace_id = COALESCE(wu.workspace_id, u.workspace_id)
            LEFT JOIN shops s
                ON s.shop_id = ws.shop_id
                AND s.active_flag = 1
            WHERE u.uid = ?
            AND ws.status = 1
            AND (
                wu.role in ('Owner', 'Admin') OR
                FIND_IN_SET(ws.shop_id, wu.shop_ids) OR wu.shop_ids IS NULL
            )
        `

        let params = [uid]
        if (selectedWorkspaceId && Number(selectedWorkspaceId) > 0) {
            query = query + `
                AND ws.workspace_id = ?
            `
            params.push(Number(selectedWorkspaceId))
        }

        query = query + `
            GROUP BY 1,2
        `
        let result = await mysql.query(query, params);
        user_shops = result
    } catch (err) {
        logger.error('user.getUserShops error : ', err)
    }

    if (!!user_shops && user_shops.length > 0) {
        await cache.set(cacheKey, user_shops)
    }
    return user_shops
}

// updatePartial function receives a subset of database columns to update
const updatePartial = async function (uid, partial_update) {

    let userObj = await getUserByUID(uid)
    if (!userObj || !userObj.uid) {
        // request id is invalid
        return {}
    }

    delete userObj.row_updated_at
    delete userObj.row_created_at

    let newUserObj = Object.assign(userObj, partial_update)

    try {
        await mysql.query("UPDATE users SET ? WHERE uid = ?", [newUserObj, uid]);
        await clearUserCache(uid)
        return newUserObj;
    } catch (err) {
        logger.error('user.updatePartial: ', err);
        return {};
    }
}

async function saveOnboardingDetails(uid, {onboard_email, onboard_name, onboard_industry, onboard_user_type, onboard_phone}) {

    if (!uid) {
        return {status : false}
    }

    onboard_email = onboard_email ?? '';
    onboard_phone = onboard_phone ?? '';
    onboard_name = onboard_name ?? '';
    onboard_industry = onboard_industry ?? '';
    onboard_user_type = onboard_user_type ?? '';


    let params = [
        onboard_email,
        onboard_name,
        onboard_phone,
        onboard_industry,
        onboard_user_type,
        uid
    ];

    try {
        await mysql.query(`
            UPDATE users
            SET onboard_email = ?,
                onboard_name = ?,
                onboard_phone = ?,
                onboard_industry = ?,
                onboard_user_type = ?
            WHERE uid = ?`,
            params
        );
        await clearUserCache(uid)
        return {status : true};
    } catch (err) {
        logger.error('user.saveOnboardingDetails: ', err);
        return {status : false}
    }
}


async function getUserByUID(uid) {
    var cacheKey = getUserByIDCacheKey(uid)
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    var user = {}
    try {
        let result = await mysql.query('SELECT * FROM users WHERE uid = ?', [uid]);
        user = result[0]
    } catch (err) {
        logger.error('user.getUserByUID error : ', err)
    }

    if (user) {
        await cache.set(cacheKey, user)
    }
    return user
}

export async function getUserByEmail(email) {
    var cacheKey = getUserByEmailCacheKey(email)
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    var user = {}
    try {
        let result = await mysql.query('SELECT * FROM users WHERE email = ?', [email]);
        user = result[0]
    } catch (err) {
        logger.error('user.getUserByEmail error : ', err)
    }

    if (user) {
        await cache.set(cacheKey, user)
    }
    return user
}

export async function save(data) {
    try {
        var connection = await mysql.connection();
    } catch (err) {
        logger.error('user.save : mysql connection failure', err);
        throw err;
    }

    let user = {
        email : data.email,
        uid : data.uid
    }

    try {
        let userData = await connection.query('SELECT * FROM users WHERE uid = ?', [user.uid]);
        if (userData.length == 0) {
            await connection.query("INSERT INTO users SET ?", user);
        } else {
            await connection.query("UPDATE users SET ? WHERE uid = ?", [user, user.uid]);
        }
        return user;
    } catch (err) {
        logger.error('user.save: ', err);
        throw err;
    } finally {
        await connection.release();
    }
}

async function sendVerificationEmail(email, link) {
    try {
        const htmlContent = await verificationEmailRender({ verificationLink: link });

        const params = {
            FromEmailAddress: 'DataDrew Support <<EMAIL>>',
            Destination: {
                ToAddresses: [email],
            },
            Content: {
                Simple: {
                    Subject: {
                        Data: 'Verify your email for DataDrew Analytics',
                        Charset: 'UTF-8',
                    },
                    Body: {
                        Html: {
                            Data: htmlContent,
                            Charset: 'UTF-8',
                        },
                    }
                }
            }
        };

        const command = new SendEmailCommand(params);
        const sendResp = await sesClient.send(command);

        logger.info(`sendVerificationEmail ${email}: `, sendResp);
        return { success: true };
    } catch (error) {
        logger.error('Failed to send verification email:', error);
        return { success: false, error: error.message || error };
    }
}

async function createWorkspace(workspace_name, workspace_type) {
    if (!workspace_name || !workspace_type) {
        logger.error('user.createWorkspace : invalid params', {
            workspace_name : workspace_name,
            workspace_type : workspace_type
        });
        return {status : false}
    }

    try {
        let result = await mysql.query("INSERT INTO workspaces SET ?", {
            name : workspace_name,
            workspace_type : workspace_type
        });
        return {status : true, workspace_id : result.insertId};
    } catch (err) {
        logger.error('user.createWorkspace: ', err);
        return {status : false}
    }
}

async function mapShopToWorkspace(workspace_id, shop_id) {

    if (!workspace_id || !shop_id) {
        logger.error('user.mapShopToUser : invalid params', {
            workspace_id : workspace_id,
            shop_id : shop_id
        });
        return {status : false}
    }

    try {
        await mysql.query("INSERT INTO workspace_shops SET ?", {
            workspace_id : workspace_id,
            shop_id : shop_id,
            status : 1
        });
        return {status : true};
    } catch (err) {
        logger.error('user.mapShopToUser: ', err);
        return {status : false}
    }
}

async function settings({user_data, user_shops}) {

    if (!user_data.uid) {
        return {status : false}
    }

    let promises = []
    for (var k in user_shops) {
        let shopObj = user_shops[k]
        promises.push(getActiveSubscription(shopObj.shop_id))
    }

    let subscriptions = await Promise.all(promises)

    let shopOptions = []
    for (var k in user_shops) {
        let shopObj = user_shops[k]
        shopOptions.push({
            shop_id : shopObj.shop_id,
            name : shopObj.name,
            domain : shopObj.domain,
            myshopify_domain : shopObj.myshopify_domain,
            subscription : subscriptions[k]
        })
    }

    return {
        status : true,
        connectedShops : shopOptions
    }
}

async function unlinkUserShop({user_data, user_shops, shop_to_unlink}) {
    if (!user_data || !user_data.uid || !shop_to_unlink || !user_data.workspace_id) {
        return {status : false}
    }

    let shopObj = await shop.getShopByOrigin(shop_to_unlink)
    if (!shopObj || !shopObj.myshopify_domain) {
        return {status : false}
    }

    // shop not mapped
    if (user_shops.filter((s) => s.shop_id == shopObj.shop_id).length == 0) {
        return {status : false}
    }

    try {
        await mysql.query("UPDATE workspace_shops SET status = 0 WHERE workspace_id = ? AND shop_id = ?", [user_data.workspace_id, shopObj.shop_id]);
        await Workspace.clearCacheAllUsers(user_data.workspace_id)
        return {status : true};
    } catch (err) {
        logger.error('user.unlinkUserShop: ', err);
        return {status : false}
    }
}

// addStoreToAccount assumes that both user and shop is authenticated
// to link it together once - so that it's available in user account
// if either is missing -> this function will throw an error
async function linkAuthedUserShop({user_data, user_shops, shop_data}) {
    if (!user_data || !user_data.uid) {
        return {status : false}
    }

    // map this shop to user if not already mapped
    if (user_shops.filter((s) => s.shop_id == shop_data.shop_id).length == 0) {
        // check if user workspace exists for this user, if not - create a new workspace with userData email

        if (!user_data.workspace_id || ![WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(user_data?.role ?? '')) {
            // NOTE: this should always be getting the owner workspace id here, since owner workspace is created at the time of user creation
            user_data.workspace_id = await getOrCreateOwnerWorkspaceID(user_data.uid, user_data.email, [shop_data.shop_id]);

            if (!user_data.workspace_id) {
                logger.error('linkAuthedUserShop : getOrCreateOwnerWorkspaceID failed', {
                    uid : user_data.uid,
                    email : user_data.email
                })
                return {status : false}
            }
        }

        let finalResponse = await mapShopToWorkspace(user_data.workspace_id, shop_data.shop_id)
        if (finalResponse.status) {
            // copy over user's onboard values to shop if missing
            if (!shop_data.onboard_email) {
                // first shop added - update user workspace type
                await shop.saveOnboardingDetails({
                    shop : shop_data,
                    onboard_email : user_data.onboard_email,
                    onboard_name : user_data.onboard_name,
                    onboard_phone : user_data.onboard_phone,
                    onboard_industry : user_data.onboard_industry,
                    onboard_user_type : user_data.onboard_user_type
                })
            }
            await Workspace.clearCacheAllUsers(user_data.workspace_id)
        }
        return finalResponse;
    } else {
        return {status : true}
    }
}

export default {
    settings,
    updatePartial,
    linkAuthedUserShop,
    unlinkUserShop,
    resolveUser,
    saveOnboardingDetails,
    sendVerificationEmail,
    getUserByEmail,
    clearUserCache,
    save
};