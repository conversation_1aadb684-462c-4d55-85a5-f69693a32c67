import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";
import SidenavCollapse from "@/examples/Sidenav/SidenavCollapse";
import { Avatar } from "antd";
import MultiStoreDialog from '@/layouts/authentication/add-store/multi-store-account';
import { AddStoreDialog } from '@/layouts/authentication/add-store';
import NiceModal from '@ebay/nice-modal-react';
import { useTranslation } from 'react-i18next';
import Button from "@mui/material/Button";
import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import Icon from "@mui/material/Icon";
import { useMaterialUIController, setSelectedShop, tracker } from "@/context";
import AddBusinessIcon from '@mui/icons-material/AddBusiness';
import { WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_OWNER } from "@/constants";

const handleSelectedShopChange = (dispatch, selectedShop) => {
    if (!selectedShop) {
        return;
    }

    tracker.event("Switch Shop", { shop: selectedShop });
    if (selectedShop == 'add-new-store') {
        // show a dialog to add a new store
        NiceModal.show(AddStoreDialog, {});
        return;
    }

    if (selectedShop == 'multi-store-sign-up') {
        // show a dialog to multi store login
        NiceModal.show(MultiStoreDialog, {});
        return;
    }

    if (selectedShop == 'multi-store-sign-in') {
        // show a dialog to multi store login
        NiceModal.show(MultiStoreDialog, {
            preferSignIn: true
        });
        return;
    }

    setSelectedShop(dispatch, selectedShop);
};

export function ShopOptionsDropdown() {
    const [controller, dispatch] = useMaterialUIController();
    const { loginConfig, selectedShop } = controller;
    const { t } = useTranslation();

    if (!loginConfig.user || !loginConfig.user.email) {
        return null;
    }

    if (!loginConfig.shopOptions || loginConfig.shopOptions.length == 0) {
        return null;
    }

    let activeShopObj = {};

    let optionList = loginConfig.shopOptions.map((s) => {
        let renderName = !!s.nameKey ? t(s.nameKey) : s.name;
        let option = { label: renderName, value: s.myshopify_domain, is_store: (s.is_store ?? false) };
        if (s.myshopify_domain == selectedShop) {
            activeShopObj = option;
        }
        return option;
    });

    return (
        <Autocomplete
            onChange={(_, newValue) => {
                if (newValue && newValue.value) {
                    handleSelectedShopChange(dispatch, newValue.value);
                }
            }}
            getOptionLabel={(option) => {
                return option.label;
            }}
            id="shop-selection"
            blurOnSelect
            isOptionEqualToValue={(option, value) => {
                return option.value === value.value;
            }}
            disableClearable={true}
            options={optionList}
            value={activeShopObj}
            fullWidth
            sx={{ width: 300 }}
            renderInput={(params) => <TextField {...params} />}
            renderOption={(props, option) => {
                return (
                    <li {...props} key={option.value}>
                        <MDBox display="flex" flexDirection="column">
                            <MDTypography variant="button" color={"dark"}>{option.label}</MDTypography>
                            <MDTypography variant="caption" fontWeight="regular" color={"dark"}>
                                {option.is_store ? option.value : ""}
                            </MDTypography>
                        </MDBox>
                    </li>
                );
            }}
        />
    );
}

export default function ProfileCollapse({ keyName, collapseOnClick, openCollapse, setOpenCollapse }) {
    const navigate = useNavigate();
    const [controller, dispatch] = useMaterialUIController();
    const { loginConfig, selectedShop, miniSidenav, selectedWorkspaceId } = controller;
    const location = useLocation();
    const { pathname } = location;
    const collapseName = pathname.split("/").slice(1)[0];
    const { t } = useTranslation();

    const isShopFlow = !loginConfig.userData || !loginConfig.userData.email;
    const activeWorkspace = (loginConfig?.workspaces ?? []).find((w) => w.workspace_id === selectedWorkspaceId);

    let shopOptions = [];
    if (isShopFlow) {
        shopOptions = loginConfig.shopOptions ?? [];
    } else {
        shopOptions = activeWorkspace?.shops ?? [];
    }

    let activeShop = shopOptions.find((shop) => shop.myshopify_domain === selectedShop);
    if (loginConfig.admin && !activeShop) {
        activeShop = (loginConfig.adminShopOptions ?? []).find((shop) => shop.myshopify_domain === selectedShop);
    }

    // Handle navigation to workspace settings
    const handleManageWorkspaces = () => {
        tracker.event("Navigate to Workspace Settings", { from: "profile_collapse" });
        setOpenCollapse(false); // Close the collapse

        // Use setTimeout to ensure the collapse state update completes before navigation
        setTimeout(() => {
            navigate("/workspace-settings");
        }, 50);
    };

    const handleAddNewStore = () => {
        NiceModal.show(AddStoreDialog, {}); // Open the Add Store modal
    };

    return (
        <SidenavCollapse
            key={keyName}
            name={activeShop?.name || activeShop?.myshopify_domain || t("no-shop-selected")}
            subtitle={!isShopFlow && openCollapse ? (activeWorkspace?.workspace_name || activeWorkspace?.name || t("my-workspace")) : null}
            icon={
                <Avatar
                    style={{
                        backgroundColor: "#e91e63",
                        verticalAlign: "middle",
                        justifyContent: "middle",
                        textTransform: "uppercase",
                        borderRadius: "50%",
                    }}
                    size="small"
                >
                    {activeShop?.name?.[0]?.toUpperCase() ||
                     activeShop?.myshopify_domain?.[0]?.toUpperCase() ||
                     (isShopFlow && selectedShop?.[0]?.toUpperCase()) ||
                     "S"}
                </Avatar>
            }
            active={collapseName === keyName}
            open={openCollapse === keyName}
            onClick={collapseOnClick}
        >
            {/* Workspace Content */}
            <MDBox pl={2} pr={2} pt={1} pb={0.5}>

                {!miniSidenav && (
                    <>
                        {/* STORES Header - Only show in user flow */}
                        {!isShopFlow && (
                            <MDBox pl={1} mb={0.5}>
                                <span style={{
                                    fontSize: "0.65rem",
                                    fontWeight: "normal",
                                    textTransform: "uppercase",
                                    letterSpacing: "1px",
                                    color: "#aaaaaa"
                                }}>
                                    {t("stores-in-your-workspace").toUpperCase()}
                                </span>
                            </MDBox>
                        )}

                        {/* Shops in the Active Workspace or Current Shop in Shop Flow */}
                        <MDBox>
                            {shopOptions.map((s) => {
                                    let renderName = !!s.nameKey ? t(s.nameKey) : s.name;
                                    let renderNameComponent = (
                                        <MDTypography
                                            variant="button"
                                            fontWeight="medium"
                                            color="white"
                                            alignItems="center"
                                            verticalAlign="middle"
                                        >
                                            {renderName}
                                        </MDTypography>
                                    );
                                    let renderIcon = (
                                        <Icon
                                            fontSize="small"
                                            sx={{
                                                color: "white !important",
                                                fontSize: "18px",
                                                marginRight: "12px",
                                                marginLeft: "2px",
                                                opacity: "0.9"
                                            }}
                                        >
                                            storefront
                                        </Icon>
                                    );

                                    if (s.myshopify_domain == "multi-store-sign-in" || s.myshopify_domain == "multi-store-sign-up" || s.myshopify_domain == "add-new-store") {
                                        renderIcon = <AddBusinessIcon color="primary" sx={{fontSize : "20px !important"}} />
                                    }

                                    return (
                                        <SidenavCollapse
                                            key={s.myshopify_domain}
                                            name={renderNameComponent}
                                            badge={null}
                                            icon={renderIcon}
                                            noCollapse={true}
                                            active={s.myshopify_domain == activeShop}
                                            sx={{
                                                pointer: "default !important",
                                                display: "flex",
                                                alignItems: "center",
                                                height: "100%"
                                            }}
                                            onClick={() => {
                                                if (s.myshopify_domain != activeShop) {
                                                    handleSelectedShopChange(dispatch, s.myshopify_domain, t)
                                                }
                                                setOpenCollapse(false)
                                            }}
                                        />
                                    )
                            })}
                        </MDBox>
                    </>
                )}

                {/* Add Store Button - Only show in user flow */}
                {!isShopFlow && [WORKSPACE_ROLE_ADMIN, WORKSPACE_ROLE_OWNER].includes(loginConfig.userData?.role) && (
                    <MDBox mt={2} mb={1} px={1}>
                        <Button
                            variant="outlined"
                            size="small"
                            fullWidth
                            onClick={handleAddNewStore}
                            startIcon={
                                <Icon fontSize="small" sx={{ color: "white" }}>add</Icon>
                            }
                            sx={{
                                color: miniSidenav ? "transparent" : "white",
                                borderColor: "rgba(255, 255, 255, 0.3)",
                                fontSize: miniSidenav ? "0" : "0.7rem",
                                padding: "4px 8px",
                                fontWeight: "medium",
                                textTransform: "uppercase",
                                minWidth: miniSidenav ? "40px" : "auto",
                                "& .MuiButton-startIcon": {
                                    margin: miniSidenav ? "0" : undefined
                                },
                                "&:hover": {
                                    borderColor: "#1976d2",
                                    backgroundColor: "rgba(25, 118, 210, 0.1)"
                                }
                            }}
                        >
                            {!miniSidenav && t("add-new-store").toUpperCase()}
                        </Button>
                    </MDBox>
                )}

                {/* Manage Workspaces Button - Only show in user flow */}
                {!isShopFlow && (
                    <MDBox mb={1} px={1}>
                        <Button
                            variant="outlined"
                            size="small"
                            fullWidth
                            onClick={handleManageWorkspaces}
                            startIcon={
                                <Icon fontSize="small" sx={{ color: "white" }}>workspaces</Icon>
                            }
                            sx={{
                                color: miniSidenav ? "transparent" : "white",
                                borderColor: "rgba(255, 255, 255, 0.3)",
                                fontSize: miniSidenav ? "0" : "0.7rem",
                                padding: "4px 8px",
                                fontWeight: "medium",
                                textTransform: "uppercase",
                                minWidth: miniSidenav ? "40px" : "auto",
                                "& .MuiButton-startIcon": {
                                    margin: miniSidenav ? "0" : undefined
                                },
                                "&:hover": {
                                    borderColor: "#1976d2",
                                    backgroundColor: "rgba(25, 118, 210, 0.1)"
                                }
                            }}
                        >
                            {!miniSidenav && t("manage-workspaces").toUpperCase()}
                        </Button>
                    </MDBox>
                )}
            </MDBox>
        </SidenavCollapse>
    );
}
