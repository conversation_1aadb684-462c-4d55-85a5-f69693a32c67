import React, { useState, useEffect } from "react";
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import Icon from "@mui/material/Icon";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import IconButton from "@mui/material/IconButton";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
import MDAvatar from "@/components/MDAvatar";
import Avatar from "boring-avatars";
import { useTranslation } from "react-i18next";
import { useMaterialUIController, fetchLoginConfig, setSelectedShop, setSelectedWorkspaceId } from "@/context";
import { axiosInstance } from "@/context";
import { toast } from "react-toastify";
import { WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN } from "@/constants";

function WorkspaceHeader({ workspaceDetails, setWorkspaceDetails, fetchWorkspaceDetails }) {
  const [controller, dispatch] = useMaterialUIController();
  const { t } = useTranslation();
  const { loginConfig } = controller;
  const { userData } = loginConfig;

  // Get workspaces from loginConfig
  const [workspaces, setWorkspaces] = useState([]);
  const [isWorkspaceModalOpen, setIsWorkspaceModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Editing state
  const [isEditingWorkspaceName, setIsEditingWorkspaceName] = useState(false);
  const [editedWorkspaceName, setEditedWorkspaceName] = useState("");
  const [isUpdatingWorkspaceName, setIsUpdatingWorkspaceName] = useState(false);

  // Initialize workspaces and current workspace on component mount
  useEffect(() => {
    if (loginConfig && loginConfig.workspaces) {
      setWorkspaces(loginConfig.workspaces);
      setLoading(false);
    }
  }, [loginConfig, dispatch]);

  // Function to handle workspace change
  const handleWorkspaceChange = (workspace) => {
    setSelectedWorkspaceId(dispatch, workspace.workspace_id);

    // Automatically select the first shop in the new workspace as the active shop
    if (workspace.shops && workspace.shops.length > 0) {
      const firstShop = workspace.shops[0].myshopify_domain;
      setSelectedShop(dispatch, firstShop); // Update the selected shop in global state
    }

    fetchLoginConfig(dispatch, true);

    setIsWorkspaceModalOpen(false); // Close the modal after switching
  };

  // Initialize edited name when workspaceDetails changes
  useEffect(() => {
    if (workspaceDetails?.workspace_name) {
      setEditedWorkspaceName(workspaceDetails.workspace_name);
    }
  }, [workspaceDetails?.workspace_name]);

  const handleEditStart = () => {
    setIsEditingWorkspaceName(true);
    setEditedWorkspaceName(workspaceDetails?.workspace_name || "");
  };

  const handleEditCancel = () => {
    setIsEditingWorkspaceName(false);
    setEditedWorkspaceName(workspaceDetails?.workspace_name || "");
  };

  const handleEditSave = async () => {
    if (!editedWorkspaceName.trim()) {
      toast.error("Workspace name is required");
      return;
    }

    if (editedWorkspaceName.trim() === workspaceDetails?.workspace_name) {
      setIsEditingWorkspaceName(false);
      return;
    }

    const newWorkspaceName = editedWorkspaceName.trim();

    // Exit edit mode and show loading state immediately
    setIsEditingWorkspaceName(false);
    setIsUpdatingWorkspaceName(true);
    try {
      const response = await axiosInstance.post('/api/workspace/update', {
        workspace_id: workspaceDetails.workspace_id,
        name: newWorkspaceName
      });

      if (response.data.status) {
        // Success: show toast and update with backend confirmation
        toast.success("Workspace name updated successfully");

        // Update local state with the confirmed name from backend
        if (setWorkspaceDetails) {
          const updatedWorkspaceDetails = {
            ...workspaceDetails,
            workspace_name: newWorkspaceName // Use the name we sent to backend
          };
          setWorkspaceDetails(updatedWorkspaceDetails);
        }

        // Background refresh to sync with backend - use silent mode to prevent loading states
        if (fetchWorkspaceDetails) {
          fetchWorkspaceDetails(true); // Silent background refresh - no loading states
        }
      } else {
        // Backend error: show error and revert to original name
        toast.error("Failed to update workspace name");
        setEditedWorkspaceName(workspaceDetails?.workspace_name || "");
      }
    } catch (error) {
      console.error('Error updating workspace name:', error);
      toast.error("Failed to update workspace name, please try again later");
      // Revert to original name on error
      setEditedWorkspaceName(workspaceDetails?.workspace_name || "");
    } finally {
      setIsUpdatingWorkspaceName(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleEditSave();
    } else if (event.key === 'Escape') {
      handleEditCancel();
    }
  };

  if (!userData || !userData.uid || loading) {
    return null;
  }

  return (
    <div id="workspace-header">
      <Card>
        <MDBox p={2}>
          <Grid container spacing={3} alignItems="center" justifyContent="space-between">
            <Grid item>
              <Grid container spacing={3} alignItems="center">
                <Grid item>
                  <MDAvatar alt="profile-image" size="xl" shadow="sm">
                    <Avatar
                      name={workspaceDetails?.workspace_name || "Workspace"}
                      variant={"bauhaus"}
                      size={120}
                      colors={["#E91E63", "#344767", "#F44335", "#FB8C00", "#f0f2f5"]}
                    />
                  </MDAvatar>
                </Grid>
                <Grid item>
                  <MDBox height="100%" mt={0.5} lineHeight={1}>
                    {isEditingWorkspaceName ? (
                      <MDBox display="flex" alignItems="center" gap={1}>
                        <TextField
                          value={editedWorkspaceName}
                          onChange={(e) => setEditedWorkspaceName(e.target.value)}
                          onKeyDown={handleKeyPress}
                          variant="outlined"
                          size="small"
                          disabled={isUpdatingWorkspaceName}
                          autoFocus
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              fontSize: '1.25rem',
                              fontWeight: 500,
                              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                              lineHeight: 1.334,
                            },
                            '& .MuiOutlinedInput-input': {
                              fontSize: '1.25rem',
                              fontWeight: 500,
                              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                              lineHeight: 1.334,
                              padding: '8px 12px',
                            }
                          }}
                        />
                        <IconButton
                          onClick={handleEditSave}
                          disabled={isUpdatingWorkspaceName}
                          size="small"
                          color="success"
                        >
                          <Icon>check</Icon>
                        </IconButton>
                        <IconButton
                          onClick={handleEditCancel}
                          disabled={isUpdatingWorkspaceName}
                          size="small"
                          color="error"
                        >
                          <Icon>close</Icon>
                        </IconButton>
                      </MDBox>
                    ) : (
                      <MDBox display="flex" alignItems="center" gap={1}>
                        {isUpdatingWorkspaceName ? (
                          // Show shimmer/skeleton loading state while updating
                          <MDBox
                            sx={{
                              width: '200px',
                              height: '32px',
                              backgroundColor: '#f0f0f0',
                              borderRadius: '4px',
                              position: 'relative',
                              overflow: 'hidden',
                              '&::after': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: '-100%',
                                width: '100%',
                                height: '100%',
                                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',
                                animation: 'shimmer 1.5s infinite',
                              },
                              '@keyframes shimmer': {
                                '0%': { left: '-100%' },
                                '100%': { left: '100%' },
                              },
                            }}
                          />
                        ) : (
                          <MDTypography variant="h5" fontWeight="medium">
                            {(workspaceDetails?.workspace_name ?? "").includes("@")
                              ? `${workspaceDetails?.workspace_name}'s workspace`
                              : workspaceDetails ? workspaceDetails?.workspace_name : t("my-workspace")}
                          </MDTypography>
                        )}
                        {workspaceDetails && [WORKSPACE_ROLE_OWNER, WORKSPACE_ROLE_ADMIN].includes(workspaceDetails.role) && !isUpdatingWorkspaceName && (
                          <IconButton
                            onClick={handleEditStart}
                            size="small"
                            sx={{ ml: 0.5 }}
                          >
                            <Icon fontSize="small">edit</Icon>
                          </IconButton>
                        )}
                      </MDBox>
                    )}
                    {workspaceDetails && (
                      <MDTypography variant="body2" color="text">
                        {workspaceDetails.role}
                        {workspaceDetails.shops && ` · ${workspaceDetails.shops.length} ${workspaceDetails.shops.length === 1 ? t("store") : t("stores")}`}
                      </MDTypography>
                    )}
                  </MDBox>
                </Grid>
              </Grid>
            </Grid>
            <Grid item>
              <MDButton
                variant="contained"
                color="secondary"
                startIcon={<Icon>swap_horiz</Icon>}
                onClick={() => setIsWorkspaceModalOpen(true)}
              >
                {t("switch-workspace")}
              </MDButton>
            </Grid>
          </Grid>
        </MDBox>
      </Card>

      {/* Workspace Selection Modal */}
      <Modal
        open={isWorkspaceModalOpen}
        onClose={() => setIsWorkspaceModalOpen(false)}
        aria-labelledby="workspace-modal-title"
        aria-describedby="workspace-modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: 400,
            bgcolor: "background.paper",
            boxShadow: 24,
            p: 4,
            borderRadius: 2,
          }}
        >
          <Typography id="workspace-modal-title" variant="h6" component="h2" mb={2}>
            {t("select-workspace")}
          </Typography>
          <Box id="workspace-modal-description" display="flex" flexDirection="column" gap={2}>
            {workspaces.map((workspace) => (
              <Card
                key={workspace.workspace_id}
                sx={{
                  p: 2,
                  cursor: "pointer",
                  "&:hover": { backgroundColor: "#f5f5f5" },
                  backgroundColor: workspaceDetails && workspaceDetails.workspace_id === workspace.workspace_id ? "#f0f2f5" : "inherit",
                }}
                onClick={() => handleWorkspaceChange(workspace)}
              >
                <Box display="flex" alignItems="center">
                  {workspace.shops && workspace.shops.length > 0 && (
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        marginRight: 2,
                        flexShrink: 0
                      }}
                    >
                      <Avatar
                        name={workspace.shops[0].name}
                        variant={"pixel"}
                        size={40}
                        colors={["#E91E63", "#344767", "#F44335", "#FB8C00", "#f0f2f5"]}
                      />
                    </Box>
                  )}
                  <Box>
                    <Typography variant="body2" fontWeight="medium">{workspace.name}</Typography>
                    <Typography variant="body2" color="text.secondary" fontWeight="regular" sx={{ fontSize: '0.8rem' }}>
                      {workspace.role}
                      {workspace.shops && ` · ${workspace.shops.length} ${workspace.shops.length === 1 ? t("store") : t("stores")}`}
                    </Typography>
                  </Box>
                </Box>
              </Card>
            ))}
          </Box>

        </Box>
      </Modal>
    </div>
  );
}

export default WorkspaceHeader;